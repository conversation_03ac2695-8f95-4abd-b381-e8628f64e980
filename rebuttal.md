Dear Editor-in-Chief:



On behalf of all the authors, I am writing to formally request approval to resubmit our revised manuscript titled “A Real-time Scale-robust Network for Glottis Segmentation in Nasal Transnasal Intubation” (Manuscript ID: TMI-2024-2303) to IEEE Transactions on Medical Imaging.

We greatly appreciate the constructive feedback provided by the Editorial Board, which has been instrumental in improving the quality of our manuscript. Guided by these suggestions, we have carefully revised the manuscript to address all the comments in detail.

The detailed point-by-point responses to the comments from the Editorial Board are included in the attached Cover Letter. We believe these revisions have significantly enhanced the clarity, scientific rigor, and overall quality of the manuscript, aligning it more closely with the high standards of the journal.

We kindly request your approval to proceed with the resubmission. If there are any additional requirements or further information needed, please do not hesitate to let us know.

Thank you for your time and consideration. We look forward to your feedback.

Sincerely,

<PERSON>

(On behalf of all authors)


我代表所有作者，正式写信申请对我们先前提交的稿件TMI-2025-1133进行申诉。在本次审稿中，两位审稿人均认可了我们的工作，表示可以发表在TMI上。而审稿人2则提出了一些存在主观偏见和事实性错误的评论，导致编辑部给出了拒稿意见。
重新提交我们修改后的稿件，题目是《用于经鼻气管插管中声门分割的实时尺度鲁棒网络》（稿件编号：TMI-2024-2303），投到IEEE Transactions on Medical Imaging（IEEE医学影像汇刊）。

我们非常感谢编辑部提出的建设性意见，这些意见对提高我们稿件的质量起到了很大的作用。根据这些建议，我们认真修改了稿件，详细地解决了所有评论中的问题。

我们把针对编辑部评论的逐条回复都写在了附件的求职信里。我们相信这些修改大大提高了稿件的清晰度、科学严谨性和整体质量，使其更符合该期刊的高标准。

我们恳请您批准我们重新提交稿件。如果还有其他要求或需要更多信息，请随时告诉我们。


Dear Editor-in-Chief:

On behalf of all authors, I am writing to formally appeal the decision regarding our previously submitted manuscript (TMI-2025-1133). Throughout the review process, two of the three reviewers provided positive assessments and recommended publication in “IEEE Transactions on Medical Imaging”. However, Reviewer 2 raised some comments that contained subjective bias and factual errors, leading the editorial board to reject the manuscript.

We believe that Reviewer 2's comments are rather one-sided and that Reviewer 2 is not familiar enough with our manuscript to fully reflect its quality. These concerns can be effectively addressed through minor revisions to the manuscript.

We respectfully request the editorial board's reconsideration of our manuscript. The work represents a significant investment in developing a comprehensive dataset and conducting extensive state-of-the-art comparative experiments, contributing foundational value to the field. To facilitate reproducibility and further research, all code and datasets associated with this work have been made publicly available. We appreciate the editorial board's consideration of this appeal and our revised submission. A detailed point-by-point response to Reviewer 2's comments is provided in the attached Cover Letter.

Thank you for your time and consideration. We look forward to your feedback.

Sincerely,

Yang Zhou

(On behalf of all authors)

对手稿TMI-2025-1133的申诉请求


我们非常感谢您的积极回复。目前我们已经按照要求在系统中提交了AdminComm，新分配的稿件号码是TMI-2025-1432。希望我们的回复能够解决审稿人2的疑虑，也希望编辑部能重新考虑我们的稿件。我们感谢您的时间并期待着您的回复。


We greatly appreciate your positive response. We have now submitted the Admin Comm in the system as requested, and the newly assigned manuscript number is TMI-2025-1432. We hope our response addresses the concerns of Reviewer 2 and that the editorial board will reconsider our manuscript. We thank you for your time and look forward to your reply.

We sincerely appreciate your constructive feedback and the opportunity to address our manuscript appeal. We have submitted the Administrative Communication through the manuscript management system as requested, with the newly assigned manuscript reference number TMI-2025-1432. We trust that our detailed response comprehensively addresses the concerns raised by Reviewer 2, and we respectfully request the Editorial Board's reconsideration of our manuscript based on the merits of our revisions. We appreciate the Editorial Board's time and consideration, and we look forward to your decision regarding our appeal.

这项工作使用了三个数据集。由内窥镜在仿体上捕获的PID数据集和新加坡中央医院82名患者的鼻咽喉镜记录组成Clinical dataset已经开源，请参见https://doi.org/10.6084/m9.figshare.26342779.v3。公开数据集BAGLS请参见https://www.x-mol.com/paperRedirect/1274088604830298112，在文中将此数据集转换为COCO格式。
算法源码和所有实验设置在手稿接受后会被开源。


This work used three datasets. The PID dataset, captured by endoscopes on a phantom, and the Clinical dataset, consisting of nasopharyngeal endoscopes recordings from 82 patients at Singapore General Hospital, have been open-sourced. Please refer to https://doi.org/10.6084/m9.figshare.26342779.v3. For the public dataset BAGLS, please refer to https://www.x-mol.com/paperRedirect/1274088604830298112. In the paper, this dataset is converted to COCO format. The code used in the manuscript will be open-sourced after the manuscript is accepted.



这项工作使用了三个不同的数据集：公开可用的BAGLS数据集(\href{https://www.x-mol.com/paperRedirect/1274088604830298112}{https://www.x-mol.com/paperRedirect/1274088604830298112})，一个通过商业气道模拟器和在受控条件下通过机器人插管系统获得的实验室采集的体模数据集，以及一个由新加坡中央医院82名患者的鼻咽喉镜记录组成的真实世界临床数据集(\href{https://doi.org/10.6084/m9.figshare.26342779.v3}{https://doi.org/10.6084/m9.figsh-are.26342779.v3})。

一个实时高精度检测算法用于机器辅助NTI视觉导航
Real-time high-precision framework for machine-assisted nasotracheal intubation visual navigation
一个实时高精度框架用于声门检测与分割

A real-time, high-precision framework for glottal detection and segmentation.

LightSRM boosts accuracy via intra-class variation reduction and scale robustness

Novel label assignment method further reduces intra-class differences and improves accuracy

Achieves state-of-the-art accuracy with 19 MB model size, and over 170 FPS speed

该手稿结构清晰，实验完整，具有很深刻的工程意义。虽然还存在着一些小问题，但是通过修改完全可以满足期刊要求。建议小修。


A real-time, high-precision framework for glottal detection and segmentation. LightSRM boosts accuracy via intra-class variation reduction and scale robustness. Novel label assignment method further reduces intra-class differences and improves accuracy. Achieves state-of-the-art accuracy with 19 MB model size, and over 170 FPS speed.


This paper presents a real-time, high-precision framework for glottal detection and segmentation. The proposed LightSRM boosts accuracy via intra-class variation reduction and scale robustness. A novel label assignment method is introduced to further reduce intra-class differences and enhance overall accuracy. The framework achieves state-of-the-art accuracy while maintaining a compact model size of 19 MB and real-time processing speed exceeding 170 FPS.$$$$



This work addresses the visual navigation requirements of vision-assisted NTI systems in resource-constrained environments by proposing a high-speed, high-precision detection framework.
This research confronts three primary challenges: the complex anatomical environment surrounding the glottis, substantial variations in glottal scale across subjects, and the critical trade-off between model accuracy and computational efficiency.
These challenges render existing solutions, including YOLO-series detectors and U-Net segmentation architectures, inadequate for achieving simultaneous high accuracy and real-time performance in this tasks.


The proposed GlottisNet framework employs a cascaded architecture of lightweight yet computationally efficient LightSRM feature extraction units. 
Through the introduction of a novel label assignment strategy, GlottisNet substantially reduces intra-class variance while simultaneously enhancing inter-class separability, thereby achieving superior detection accuracy. 
Regarding computational efficiency, GlottisNet employs a coupled architecture integrating both detection and segmentation heads within a unified framework.
In contrast to traditional segmentation networks that perform exhaustive pixel-wise computations across entire images, GlottisNet employs a region-of-interest approach, first generating detection bounding boxes and subsequently performing mask prediction exclusively within these localized regions, thereby substantially reducing computational overhead.

Critically, GlottisNet integrates both object detection and instance segmentation capabilities, providing essential redundancy for robust NTI visual navigation systems.
Under challenging clinical conditions, such as complete glottal closure, segmentation-based approaches may fail to generate reliable masks. 
Nevertheless, GlottisNet maintains navigational reliability by providing spatial coordinates through bounding box centroids, ensuring continuous guidance for robotic intubation systems.
Experimental validation presented in Tables 4 and 5 demonstrates that existing dual-task networks incorporating both detection and segmentation functionalities fail to achieve real-time performance on resource-constrained CPU platforms.
Conversely, while YOLO-series architectures maintain computational efficiency, they exhibit suboptimal performance in fine-grained segmentation tasks.
In comparison, GlottisNet achieves superior performance across both metrics, maintaining high detection accuracy while delivering real-time detection capabilities in resource-constrained environments, thereby establishing a decisive advantage for practical clinical deployment.


In future research, we aim to develop novel algorithmic approaches that address the specific challenges of glottis segmentation in occluded views and complex clinical environments.
Additionally, we will focus on deploying GlottisNet to edge computing devices (e.g., Jetson Nano and Raspberry Pi) for mobile NTI applications while further optimizing the segmentation branch to enhance its performance characteristics.


本研究提出了 GlottisNet，一个高速、高精度的检测与分割框架，旨在解决经鼻气管插管（NTI）中的关键安全性和效率挑战。NTI 是一项要求很高的临床操作，未能准确快速地定位声门可能导致患者创伤、手术时间延长以及缺氧风险增加。GlottisNet 的主要临床目标是作为一个强大的视觉导航辅助工具，尤其是在资源有限的医疗环境中，通过克服解剖结构复杂、声门尺寸差异巨大以及现有方案在准确性与速度之间难以权衡等关键障碍。

为实现这一目标，GlottisNet 采用了一种新颖且计算高效的架构。与计算成本高昂的传统分割网络或缺乏精细分割能力的 YOLO 系列检测网络不同，GlottisNet 采用了“先检测后分割”的耦合策略。它首先快速识别包含声门的感兴趣区域，然后仅在该区域内进行精确分割。这种设计不仅仅是技术上的优化，更是实现实际临床部署的关键。它使得 GlottisNet 能够在标准的 CPU 平台上实时运行，这意味着它可以被集成到现有的内窥镜设备中，而无需昂贵、专用的 GPU 硬件。这极大地降低了许多医院和诊所采纳这项技术的门槛。甚至可以部署在便携设备上，为在野外救援任务提供帮助。

GlottisNet 最重要的临床影响在于其内置的冗余机制，这极大地提升了操作的安全性。 我们的框架同时提供精确的分割掩码和稳健的检测边界框。在具有挑战性的临床场景中，例如当声门因分泌物遮挡或暂时闭合时，分割掩码可能会失效。在这些关键时刻，GlottisNet 的检测头仍能持续提供一个稳定的边界框，确保对临床医生或机器人系统的导航引导永不中断。这种“安全网”特性对于维持医生的信心，并在插管最困难的阶段确保连续、可靠的引导至关重要。如我们的实验所示（表\ref{SOAT}，表\ref{SOAT_modelsize}），现有的双任务网络无法在 CPU 上实时提供这种可靠性，这使得 GlottisNet 在真实世界的临床应用中具有决定性优势。

我们的综合评估证实了 GlottisNet 卓越的效率-性能权衡。如图\ref{Bubble}和图\ref{Bubble1}所示，我们的模型在检测和分割任务上均达到了顶尖的准确率，同时仅需极少的计算资源（最低的 FLOPs 和最小的模型大小）。这种高效率对于其在移动或便携式 NTI 系统中的应用至关重要，例如床边内窥镜或供住院医师使用的低成本训练模拟器，因为这些设备中的计算能力通常是有限的。

尽管 GlottisNet 在不同数据集上表现稳健（图\ref{res}），我们承认其存在局限性。在严重遮挡的情况下，模型偶尔会产生假阳性或假阴性。从临床角度来看，一个假阳性可能会错误地引导操作器械，这突显了将该系统用作决策支持工具的重要性——即辅助而非取代经验丰富的临床医生的最终判断。

未来的工作将集中于提高模型在这些挑战性情况（特别是遮挡）下的鲁棒性。我们的最终目标是将 GlottisNet 部署到边缘计算设备（如 Jetson Nano）上，为新一代智能、便携且易于获取的 NTI 导航系统铺平道路。通过进一步优化分割分支，我们旨在为临床医生提供一个更可靠、更准确的工具，以改善 NTI 手术中的患者预后。


This work presents GlottisNet, a realtime high-precision detection and segmentation framework designed to address critical safety and efficiency challenges in NTI. NTI is a demanding clinical procedure where failure to accurately and rapidly locate the glottis can lead to patient trauma, prolonged procedure times, and an increased risk of hypoxia. The primary clinical goal of GlottisNet is to serve as a robust visual navigation aid, particularly in resource-constrained medical environments, by overcoming key obstacles such as complex anatomy, significant scale variations of the glottis, and the trade-off between accuracy and speed that limits current solutions.

To achieve this, GlottisNet employs a novel and computationally efficient architecture. Unlike traditional segmentation networks that are computationally expensive, or detection networks like the YOLO-series that lack fine-grained accuracy, GlottisNet utilizes a coupled detection-then-segmentation strategy. It first rapidly identifies a region-of-interest containing the glottis and then performs precise segmentation only within this area. This design is not merely a technical optimization; it is the key to practical clinical deployment. It enables GlottisNet to run in real-time on standard CPU platforms, meaning it can be integrated into existing endoscopic equipment without requiring costly, specialized GPU hardware. This significantly lowers the barrier to adoption for many hospitals and clinics. It can even be deployed on portable devices to assist in wilderness rescue missions.

The most significant clinical impact of GlottisNet lies in its built-in redundancy, which enhances procedural safety. Our framework provides both a precise segmentation mask and a robust detection bounding box. In challenging clinical scenarios, such as when the glottis is momentarily closed or obscured by secretions, segmentation masks can fail. In these critical moments, GlottisNet's detection head continues to provide a stable bounding box, ensuring the navigational guidance for the clinician or a robotic system is never lost. This "safety net" feature is crucial for maintaining clinician confidence and ensuring continuous, reliable guidance throughout the most difficult phases of intubation. As our experiments show (Table~\ref{SOAT}, Table~\ref{SOAT_modelsize}), existing dual-task networks cannot offer this reliability in real-time on a CPU, giving GlottisNet a decisive advantage for real-world clinical use.

Our comprehensive evaluation confirms the exceptional efficiency-performance trade-off of GlottisNet. As illustrated in Fig.\ref{Bubble} and Fig.\ref{Bubble1}, our model achieves state-of-the-art accuracy in both detection and segmentation tasks while demanding minimal computational resources (the lowest FLOPs and smallest model size). This high efficiency is paramount for its application in mobile or portable NTI systems, such as bedside endoscopes or low-cost training simulators for medical residents, where computational power is inherently limited.

Despite its robust performance across diverse datasets (Fig.~\ref{res}), we acknowledge the limitations of GlottisNet. Under conditions of severe occlusion, the model can occasionally produce false positives or negatives. From a clinical perspective, a false positive could potentially misdirect the instrument, highlighting the importance of this system being used as a decision-support tool to assist, not replace, the final judgment of an experienced clinician.

Future work will focus on improving the model's robustness against these challenging cases, particularly occlusions. Our ultimate goal is the deployment of GlottisNet onto edge computing devices (e.g., Jetson Nano), paving the way for a new generation of intelligent, portable, and accessible NTI navigation systems. By further optimizing the segmentation branch, we aim to provide clinicians with an even more reliable and accurate tool to improve patient outcomes in NTI procedures.


This work presents GlottisNet, a real-time, high-precision detection and segmentation framework specifically designed to address critical safety and efficiency challenges in NTI.
NTI represents a technically demanding clinical procedure wherein failure to accurately and rapidly localize the glottis can result in patient trauma, extended procedure duration, and elevated risk of hypoxemia.
The primary clinical objective of GlottisNet is to function as a robust visual navigation aid, particularly in resource-limited medical environments, by addressing fundamental challenges including complex anatomical structures, substantial inter-patient glottal scale variations, and the accuracy-speed trade-offs that constrain existing solutions.

To accomplish this objective, GlottisNet implements a novel, computationally efficient architecture.
In contrast to computationally intensive traditional segmentation networks or detection frameworks such as the YOLO series that sacrifice fine-grained precision, GlottisNet employs a coupled detection-then-segmentation methodology.
The framework initially conducts rapid localization of bounding boxes that encompass the glottal region, subsequently performing high-precision segmentation exclusively within these identified regions of interest.
This architectural design transcends mere technical optimization, representing the cornerstone of practical clinical implementation.
It enables GlottisNet to achieve real-time performance on standard CPU platforms, facilitating seamless integration into existing endoscopic equipment without necessitating expensive, specialized GPU hardware.
This substantially reduces adoption barriers across diverse healthcare institutions, including hospitals and clinics with limited computational resources.
The framework can be deployed on portable devices to support emergency medical interventions, including wilderness rescue operations.

The most substantial clinical contribution of GlottisNet resides in its inherent redundancy mechanisms, which significantly enhance procedural safety.
The framework simultaneously delivers precise segmentation masks and robust detection bounding boxes, providing dual-modal output.
In challenging clinical scenarios, including transient glottal closure or obstruction by secretions, segmentation masks can fail.
During these critical instances, GlottisNet's detection head continues to provide a stable bounding box, ensuring uninterrupted navigational guidance for clinicians or robotic intubation systems.
This redundant capability is essential for preserving clinician confidence while ensuring continuous, dependable guidance throughout the most technically challenging phases of intubation.
As demonstrated in our experimental validation (Table~\ref{SOAT} and Table~\ref{SOAT_modelsize}), existing dual-task architectures fail to deliver comparable reliability in real-time CPU environments, conferring GlottisNet a decisive advantage for clinical deployment.

Our comprehensive experimental evaluation validates the exceptional efficiency-performance optimization achieved by GlottisNet.
As demonstrated in Fig.\ref{Bubble} and Fig.\ref{Bubble1}, the proposed model achieves SOTA performance across both detection and segmentation tasks while requiring minimal computational resources, characterized by the lowest FLOPs and most compact model size among compared methods.
This computational efficiency is paramount for deployment in mobile or portable NTI systems, including bedside endoscopic equipment and cost-effective training simulators for medical education, where computational resources are inherently constrained.

Despite its robust performance across diverse datasets (Fig.~\ref{res}), we acknowledge the limitations of GlottisNet.
Under conditions of severe anatomical occlusion or obstruction, the model may occasionally generate false positive or false negative predictions.
From a clinical safety perspective, false positive predictions could potentially misdirect surgical instrumentation, emphasizing the critical importance of deploying this system as a clinical decision-support tool to augment, rather than supersede, the expert judgment of experienced clinicians.
Future research directions will concentrate on enhancing model robustness against these challenging clinical scenarios, with particular emphasis on occlusion-resistant algorithms.
Our long-term objective encompasses the deployment of GlottisNet on edge computing platforms (e.g., NVIDIA Jetson Nano), establishing the foundation for next-generation intelligent, portable, and universally accessible NTI navigation systems.
Through continued optimization of the segmentation branch, we aspire to deliver increasingly reliable and precise clinical tools that enhance patient outcomes and procedural success rates in NTI interventions.